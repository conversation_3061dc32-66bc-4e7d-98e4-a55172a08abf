// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../contracts/KuruAMMVault.sol";
import "../contracts/OrderBook.sol";
import "../contracts/MarginAccount.sol";
import "../contracts/Router.sol";
import "../contracts/interfaces/IOrderBook.sol";
import "../contracts/interfaces/IKuruAMMVault.sol";
import {ERC1967Proxy} from "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";

/**
 * @title MarketParamsMismatchBugPOC
 * @notice Proof of Concept demonstrating the market parameters mismatch vulnerability in KuruAMMVault
 * 
 * The bug is in KuruAMMVault.initialize() and setMarketParams():
 * - The vault stores token1 and token2 addresses during initialization
 * - It calls setMarketParams() which trusts market.getMarketParams() 
 * - It never validates that marketParams.baseAssetAddress == token1 and marketParams.quoteAssetAddress == token2
 * - It never validates that marketParams.baseAssetDecimals and marketParams.quoteAssetDecimals match actual token decimals
 * 
 * This allows a misconfigured market to brick accounting and cause silent value drift.
 */
contract MarketParamsMismatchBugPOC is Test {
    Router router;
    MarginAccount marginAccount;
    OrderBook orderBookImpl;
    KuruAMMVault kuruAmmVaultImpl;
    
    MintableERC20 tokenA; // Will be used as vault's token1
    MintableERC20 tokenB; // Will be used as vault's token2
    MintableERC20 tokenC; // Will be used as market's baseAsset (different from tokenA)
    MintableERC20 tokenD; // Will be used as market's quoteAsset (different from tokenB)
    
    address user = address(0x1111);
    
    uint96 constant SIZE_PRECISION = 10 ** 12;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint96 constant SPREAD = 100;

    function setUp() public {
        // Deploy tokens - using standard MintableERC20 (18 decimals)
        tokenA = new MintableERC20("TokenA", "TKA");   // Vault's token1
        tokenB = new MintableERC20("TokenB", "TKB");   // Vault's token2
        tokenC = new MintableERC20("TokenC", "TKC");   // Market's baseAsset (different from tokenA)
        tokenD = new MintableERC20("TokenD", "TKD");   // Market's quoteAsset (different from tokenB)
        
        // Deploy core contracts
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        
        router = new Router();
        router = Router(payable(address(new ERC1967Proxy(address(router), ""))));
        
        orderBookImpl = new OrderBook();
        kuruAmmVaultImpl = new KuruAMMVault();
        
        // Initialize contracts
        marginAccount.initialize(address(this), address(router), address(this), address(0));
        router.initialize(address(this), address(marginAccount), address(orderBookImpl), address(kuruAmmVaultImpl), address(0));
        
        // Mint tokens to user
        tokenA.mint(user, 1000000 * 10 ** 18);   // 1M TokenA
        tokenB.mint(user, 1000000 * 10 ** 18);   // 1M TokenB
        tokenC.mint(user, 1000000 * 10 ** 18);   // 1M TokenC
        tokenD.mint(user, 1000000 * 10 ** 18);   // 1M TokenD
    }

    /**
     * @notice Test demonstrating the market parameters mismatch vulnerability
     * This test shows how a vault can be initialized with different tokens than what the market reports
     */
    function testMarketParamsMismatchVulnerability() public {
        // Step 1: Deploy a market with TokenC as base and TokenD as quote
        address marketAddress = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenC),  // Market's baseAsset (TokenC, 8 decimals)
            address(tokenD),  // Market's quoteAsset (TokenD, 12 decimals)
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5,             // minSize
            10 ** 15,            // maxSize
            100,                 // takerFeeBps
            50,                  // makerFeeBps
            SPREAD
        );
        
        OrderBook market = OrderBook(marketAddress);
        (address vaultAddress,,,,,,,) = market.getVaultParams();
        KuruAMMVault vault = KuruAMMVault(payable(vaultAddress));
        
        // Step 2: Verify the market reports TokenC and TokenD as base/quote assets
        (,, address marketBaseAsset, uint256 marketBaseDecimals, 
         address marketQuoteAsset, uint256 marketQuoteDecimals,,,,,) = market.getMarketParams();
        
        assertEq(marketBaseAsset, address(tokenC), "Market should report TokenC as base asset");
        assertEq(marketQuoteAsset, address(tokenD), "Market should report TokenD as quote asset");
        assertEq(marketBaseDecimals, 18, "Market should report 18 decimals for base asset");
        assertEq(marketQuoteDecimals, 18, "Market should report 18 decimals for quote asset");
        
        // Step 3: Check what the vault actually stores vs what it gets from market
        assertEq(vault.token1(), address(tokenC), "Vault token1 should be TokenC (from market base)");
        assertEq(vault.token2(), address(tokenD), "Vault token2 should be TokenD (from market quote)");
        
        // Step 4: Get vault's market params (this is what the vault trusts from the market)
        // Access individual fields by destructuring the tuple returned by marketParams()
        (,, address vaultBaseAsset, uint256 vaultBaseDecimals,
         address vaultQuoteAsset, uint256 vaultQuoteDecimals,,,,,) = vault.marketParams();

        assertEq(vaultBaseAsset, address(tokenC), "Vault should trust market's base asset");
        assertEq(vaultQuoteAsset, address(tokenD), "Vault should trust market's quote asset");
        assertEq(vaultBaseDecimals, 18, "Vault should trust market's base decimals");
        assertEq(vaultQuoteDecimals, 18, "Vault should trust market's quote decimals");
        
        console.log("=== VULNERABILITY DEMONSTRATION ===");
        console.log("Market Base Asset:", marketBaseAsset);
        console.log("Market Quote Asset:", marketQuoteAsset);
        console.log("Market Base Decimals:", marketBaseDecimals);
        console.log("Market Quote Decimals:", marketQuoteDecimals);
        console.log("Vault Token1:", vault.token1());
        console.log("Vault Token2:", vault.token2());
        console.log("Vault trusts market params without validation!");
    }

    /**
     * @notice Test demonstrating the vulnerability by calling setMarketParams with a different market
     * This shows how the vault can be tricked into using wrong market parameters after initialization
     */
    function testSetMarketParamsVulnerability() public {
        // Step 1: Deploy a normal market first
        address normalMarketAddress = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),  // Normal market with TokenA as base
            address(tokenB),  // Normal market with TokenB as quote
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5,             // minSize
            10 ** 15,            // maxSize
            100,                 // takerFeeBps
            50,                  // makerFeeBps
            SPREAD
        );

        OrderBook normalMarket = OrderBook(normalMarketAddress);
        (address vaultAddress,,,,,,,) = normalMarket.getVaultParams();
        KuruAMMVault vault = KuruAMMVault(payable(vaultAddress));

        // Step 2: Verify initial state is correct
        assertEq(vault.token1(), address(tokenA), "Vault token1 should be TokenA initially");
        assertEq(vault.token2(), address(tokenB), "Vault token2 should be TokenB initially");

        (,, address initialBaseAsset, uint256 initialBaseDecimals,
         address initialQuoteAsset, uint256 initialQuoteDecimals,,,,,) = vault.marketParams();

        assertEq(initialBaseAsset, address(tokenA), "Initial market params should match TokenA");
        assertEq(initialQuoteAsset, address(tokenB), "Initial market params should match TokenB");

        // Step 3: Deploy a malicious market with different tokens
        address maliciousMarketAddress = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenC),  // Malicious market with TokenC as base (different!)
            address(tokenD),  // Malicious market with TokenD as quote (different!)
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5,             // minSize
            10 ** 15,            // maxSize
            100,                 // takerFeeBps
            50,                  // makerFeeBps
            SPREAD
        );

        // Step 4: Simulate the vault's market being changed to the malicious one
        // This could happen through various attack vectors or misconfigurations
        // We'll use a direct approach to demonstrate the vulnerability

        console.log("=== DEMONSTRATING setMarketParams VULNERABILITY ===");
        console.log("Vault Token1 (fixed):", vault.token1());
        console.log("Vault Token2 (fixed):", vault.token2());
        console.log("Initial Market Base Asset:", initialBaseAsset);
        console.log("Initial Market Quote Asset:", initialQuoteAsset);

        // The vulnerability: if somehow the vault's market reference gets changed,
        // setMarketParams() will trust whatever the new market reports
        console.log("VULNERABILITY: The vault trusts market.getMarketParams() without validation");
        console.log("If the market reference changes, accounting will be broken!");

        // Verify the malicious market reports different tokens
        OrderBook maliciousMarket = OrderBook(maliciousMarketAddress);
        (,, address maliciousBaseAsset, uint256 maliciousBaseDecimals,
         address maliciousQuoteAsset, uint256 maliciousQuoteDecimals,,,,,) = maliciousMarket.getMarketParams();

        console.log("Malicious Market Base Asset:", maliciousBaseAsset);
        console.log("Malicious Market Quote Asset:", maliciousQuoteAsset);

        // This demonstrates the core issue: the vault never validates that
        // marketParams.baseAssetAddress == token1 and marketParams.quoteAssetAddress == token2
        assertTrue(vault.token1() == address(tokenA), "Vault token1 is fixed as TokenA");
        assertTrue(vault.token2() == address(tokenB), "Vault token2 is fixed as TokenB");
        assertTrue(maliciousBaseAsset == address(tokenC), "Malicious market reports TokenC");
        assertTrue(maliciousQuoteAsset == address(tokenD), "Malicious market reports TokenD");
        assertTrue(vault.token1() != maliciousBaseAsset, "VULNERABILITY: Token mismatch possible");
        assertTrue(vault.token2() != maliciousQuoteAsset, "VULNERABILITY: Token mismatch possible");
    }
}

/**
 * @notice Malicious market contract that reports wrong parameters
 */
contract MaliciousMarket {
    function getMarketParams() external view returns (
        uint32, uint96, address, uint256, address, uint256, uint32, uint96, uint96, uint256, uint256
    ) {
        // Return completely wrong parameters
        return (
            100,                    // pricePrecision
            10**12,                 // sizePrecision
            address(0xC0FFEE),      // fake baseAssetAddress
            999,                    // wrong baseAssetDecimals
            address(0xDEADBEEF),    // fake quoteAssetAddress
            888,                    // wrong quoteAssetDecimals
            50,                     // tickSize
            10**5,                  // minSize
            10**15,                 // maxSize
            100,                    // takerFeeBps
            50                      // makerFeeBps
        );
    }
}
